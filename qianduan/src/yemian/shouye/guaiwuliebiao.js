import { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useUnmount, useLatest } from 'ahooks';
import { meiti_chaxun, useZhuomianduanJiance } from '../../gongju/shebeishiPei_gongju.js';

// 怪物列表容器
const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = styled.div`
  width: 100%;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(145deg, ${props.theme.yanse.danjinse}12, ${props.theme.yanse.danjinse_qian}08, transparent)`
    : `linear-gradient(145deg, rgba(255,255,255,0.95), rgba(248,250,252,0.9))`};
  border: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}35`
    : 'rgba(226,232,240,0.8)'};
  border-radius: 20px;
  overflow: visible;
  margin-bottom: 24px;
  margin-right: 20px;
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 8px 32px ${props.theme.yanse.danjinse}15, 0 2px 8px rgba(0,0,0,0.1)`
    : '0 8px 32px rgba(0,0,0,0.06), 0 2px 8px rgba(0,0,0,0.04)'};
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(90deg, transparent, ${props.theme.yanse.danjinse}40, transparent)`
      : 'linear-gradient(90deg, transparent, rgba(59,130,246,0.3), transparent)'};
  }

  ${meiti_chaxun.shouji} {
    width: 100%;
    max-width: none;
    border-radius: 16px;
    margin: 0 12px 16px 0;
  }

  ${meiti_chaxun.pingban} {
    border-radius: 18px;
    margin: 0 16px 20px 0;
    max-width: 100%;
  }

  ${meiti_chaxun.zhuomian} {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    margin: 0 20px 24px 0;
  }
`;

// 标题区域
const Biaotiquyu = styled.div`
  padding: 12px 16px;
  border-bottom: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}25`
    : 'rgba(226,232,240,0.6)'};
  display: flex;
  align-items: center;
  gap: 10px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}08, transparent)`
    : 'linear-gradient(135deg, rgba(255,255,255,0.8), rgba(248,250,252,0.4))'};
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 16px;
    right: 16px;
    height: 1px;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(90deg, transparent, ${props.theme.yanse.danjinse}30, transparent)`
      : 'linear-gradient(90deg, transparent, rgba(59,130,246,0.2), transparent)'};
  }

  ${meiti_chaxun.shouji} {
    padding: 10px 14px;
    gap: 8px;

    &::after {
      left: 14px;
      right: 14px;
    }
  }

  ${meiti_chaxun.pingban} {
    padding: 11px 15px;
    gap: 9px;

    &::after {
      left: 15px;
      right: 15px;
    }
  }
`;

// 怪物数据图标
const Guaiwutubiao = styled.div`
  width: 28px;
  height: 28px;
  border-radius: 8px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}90, ${props.theme.yanse.danjinse_qian}70)`
    : 'linear-gradient(135deg, #3b82f6, #1d4ed8)'};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  flex-shrink: 0;
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 2px 8px ${props.theme.yanse.danjinse}25`
    : '0 2px 8px rgba(59,130,246,0.2)'};
  position: relative;

  &::before {
    content: '';
    position: absolute;
    inset: 1px;
    border-radius: 7px;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}95, ${props.theme.yanse.danjinse_qian}75)`
      : 'linear-gradient(135deg, #60a5fa, #3b82f6)'};
  }

  span {
    position: relative;
    z-index: 1;
  }

  ${meiti_chaxun.shouji} {
    width: 24px;
    height: 24px;
    font-size: 12px;
    border-radius: 7px;

    &::before {
      border-radius: 6px;
    }
  }

  ${meiti_chaxun.pingban} {
    width: 26px;
    height: 26px;
    font-size: 13px;
    border-radius: 7px;

    &::before {
      border-radius: 6px;
    }
  }
`;

// 标题文字
const Biaotiwenzi = styled.h3`
  margin: 0;
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  flex: 1;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}95, ${props.theme.yanse.danjinse_qian}80)`
    : 'linear-gradient(135deg, #1e293b, #475569)'};
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.3px;

  ${meiti_chaxun.shouji} {
    font-size: ${props => props.theme.ziti.daxiao.xiao};
    letter-spacing: 0.2px;
  }

  ${meiti_chaxun.pingban} {
    font-size: ${props => props.theme.ziti.daxiao.xiao};
    letter-spacing: 0.25px;
  }
`;

// 怪物列表滚动容器
const Guaiwugundongrongqi = styled.div`
  padding: 16px 20px 16px 20px;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  position: relative;
  box-sizing: border-box;

  ${meiti_chaxun.shouji} {
    padding: 16px 16px 16px 16px;
    max-width: 100%;
    box-sizing: border-box;
  }

  ${meiti_chaxun.pingban} {
    padding: 16px 18px 16px 18px;
    max-width: 100%;
    box-sizing: border-box;
  }

  ${meiti_chaxun.zhuomian} {
    max-width: 100%;
    padding: 16px 20px 16px 20px;
    box-sizing: border-box;
  }
`;

// 怪物列表容器
const Guaiwuliebiaoliebiao = styled.div`
  display: flex;
  gap: 12px;
  transition: transform 0.5s ease-in-out;
  align-items: flex-start;

  ${meiti_chaxun.shouji} {
    gap: 8px;
  }

  ${meiti_chaxun.pingban} {
    gap: 10px;
  }

  ${meiti_chaxun.zhuomian} {
    gap: 12px;
  }
`;

// 怪物卡片
const Guaiwukapian = styled(motion.div)`
  min-width: ${props => props.dongkuaipiankuandu || 160}px;
  width: ${props => props.dongkuaipiankuandu || 160}px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(145deg, ${props.theme.yanse.danjinse}12, ${props.theme.yanse.danjinse_qian}06, transparent)`
    : 'linear-gradient(145deg, rgba(255,255,255,0.95), rgba(248,250,252,0.8))'};
  border: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}25`
    : 'rgba(226,232,240,0.6)'};
  border-radius: 12px;
  padding: 14px;
  cursor: pointer;
  transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(90deg, transparent, ${props.theme.yanse.danjinse}40, transparent)`
      : 'linear-gradient(90deg, transparent, rgba(59,130,246,0.3), transparent)'};
  }

  &:hover {
    transform: translateY(-2px) scale(1.01);
    box-shadow: ${props => props.theme.mingcheng === 'anhei'
      ? `0 8px 24px ${props.theme.yanse.danjinse}20, 0 2px 8px rgba(0,0,0,0.1)`
      : '0 8px 24px rgba(0,0,0,0.06), 0 2px 8px rgba(59,130,246,0.12)'};
    border-color: ${props => props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}50`
      : 'rgba(59,130,246,0.4)'};
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(145deg, ${props.theme.yanse.danjinse}15, ${props.theme.yanse.danjinse_qian}08, transparent)`
      : 'linear-gradient(145deg, rgba(255,255,255,0.98), rgba(248,250,252,0.9))'};
  }

  ${meiti_chaxun.shouji} {
    min-width: ${props => Math.max(props.dongkuaipiankuandu || 130, 100)}px;
    width: ${props => Math.max(props.dongkuaipiankuandu || 130, 100)}px;
    padding: 12px;
    border-radius: 10px;

    &:hover {
      transform: scale(1.01);
    }
  }

  ${meiti_chaxun.pingban} {
    min-width: ${props => Math.max(props.dongkuaipiankuandu || 150, 120)}px;
    width: ${props => Math.max(props.dongkuaipiankuandu || 150, 120)}px;
    padding: 13px;
    border-radius: 11px;
  }
`;

// 怪物头像
const Guaiwutouxiang = styled.div`
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}25, ${props.theme.yanse.danjinse_qian}15)`
    : 'linear-gradient(135deg, rgba(59,130,246,0.15), rgba(147,197,253,0.1))'};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin: 0 auto 10px auto;
  border: 1.5px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}35`
    : 'rgba(59,130,246,0.2)'};
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 2px 8px ${props.theme.yanse.danjinse}15, inset 0 1px 0 ${props.theme.yanse.danjinse}25`
    : '0 2px 8px rgba(59,130,246,0.08), inset 0 1px 0 rgba(255,255,255,0.4)'};
  position: relative;
  transition: all 0.3s ease;

  &::before {
    content: '';
    position: absolute;
    inset: 1.5px;
    border-radius: 10px;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}15, ${props.theme.yanse.danjinse_qian}08)`
      : 'linear-gradient(135deg, rgba(255,255,255,0.8), rgba(248,250,252,0.6))'};
  }

  span {
    position: relative;
    z-index: 1;
  }

  ${meiti_chaxun.shouji} {
    width: 36px;
    height: 36px;
    font-size: 16px;
    margin-bottom: 8px;
    border-radius: 10px;
    border-width: 1px;

    &::before {
      border-radius: 9px;
    }
  }

  ${meiti_chaxun.pingban} {
    width: 42px;
    height: 42px;
    font-size: 18px;
    margin-bottom: 9px;
    border-radius: 11px;

    &::before {
      border-radius: 10px;
    }
  }
`;

// 怪物名称
const Guaiwumingcheng = styled.div`
  font-size: ${props => props.theme.ziti.daxiao.xiao};
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  text-align: center;
  margin-bottom: 8px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.2px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}90, ${props.theme.yanse.danjinse_qian}70)`
    : 'linear-gradient(135deg, #1e293b, #475569)'};
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  ${meiti_chaxun.shouji} {
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
    margin-bottom: 6px;
    letter-spacing: 0.1px;
  }

  ${meiti_chaxun.pingban} {
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
    margin-bottom: 7px;
    letter-spacing: 0.15px;
  }
`;

// 怪物等级
const Guaiwudengji = styled.div`
  font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  color: ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}95`
    : '#3b82f6'};
  text-align: center;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}15, ${props.theme.yanse.danjinse_qian}08)`
    : 'linear-gradient(135deg, rgba(59,130,246,0.1), rgba(147,197,253,0.05))'};
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  border: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}25`
    : 'rgba(59,130,246,0.2)'};
  letter-spacing: 0.3px;
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 1px 3px ${props.theme.yanse.danjinse}12`
    : '0 1px 3px rgba(59,130,246,0.08)'};

  ${meiti_chaxun.shouji} {
    font-size: 10px;
    padding: 3px 6px;
    border-radius: 5px;
    letter-spacing: 0.2px;
  }

  ${meiti_chaxun.pingban} {
    font-size: 11px;
    padding: 3px 7px;
    border-radius: 5px;
    letter-spacing: 0.25px;
  }
`;

// 加载状态
const Jiazaizhuangtai = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: ${props => props.theme.yanse.wenzi_ciyao};
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};

  ${meiti_chaxun.shouji} {
    padding: 30px 16px;
    font-size: ${props => props.theme.ziti.daxiao.xiao};
  }
`;

// 错误状态
const Cuowuzhuangtai = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: ${props => props.theme.yanse.cuowu};
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};

  ${meiti_chaxun.shouji} {
    padding: 30px 16px;
    font-size: ${props => props.theme.ziti.daxiao.xiao};
  }
`;

// 怪物列表组件
function Guaiwuliebiao() {
  const [guaiwushuju, shezhi_guaiwushuju] = useState([]);
  const [jiazaizhong, shezhi_jiazaizhong] = useState(true);
  const [cuowu, shezhi_cuowu] = useState(null);

  // 设备检测
  const { jiance_zhuomianduan } = useZhuomianduanJiance();

  // 滚动相关状态和引用
  const gundongrongqiref = useRef(null);
  const [chuchuruyu, shezhi_chuchuruyu] = useState(false);
  const [shubiaoruyu, shezhi_shubiaoruyu] = useState(false);
  const [yingaixuanfu, shezhi_yingaixuanfu] = useState(false);
  const xunhuangundongref = useRef(null);
  const xuanfudingshiqiref = useRef(null);
  const latestchuchuruyu = useLatest(chuchuruyu);
  const latestyingaixuanfu = useLatest(yingaixuanfu);

  // 容器宽度计算相关状态
  const [dongkuaipiankuandu, shezhi_dongkuaipiankuandu] = useState(160);

  // 模拟怪物数据
  const moniGuaiwushuju = [
    { guaiwu_id: 1001, guaiwu_mingcheng: '波利', level: 1, yuansu: ['无'], zhongzu: ['植物'] },
    { guaiwu_id: 1002, guaiwu_mingcheng: '小强', level: 2, yuansu: ['地'], zhongzu: ['昆虫'] },
    { guaiwu_id: 1003, guaiwu_mingcheng: '蘑菇宝贝', level: 3, yuansu: ['地'], zhongzu: ['植物'] },
    { guaiwu_id: 1004, guaiwu_mingcheng: '红蝙蝠', level: 5, yuansu: ['暗'], zhongzu: ['动物'] },
    { guaiwu_id: 1005, guaiwu_mingcheng: '绿水母', level: 4, yuansu: ['水'], zhongzu: ['鱼贝'] },
    { guaiwu_id: 1006, guaiwu_mingcheng: '小鸡', level: 1, yuansu: ['无'], zhongzu: ['动物'] },
    { guaiwu_id: 1007, guaiwu_mingcheng: '蚯蚓', level: 2, yuansu: ['地'], zhongzu: ['昆虫'] },
    { guaiwu_id: 1008, guaiwu_mingcheng: '树精', level: 6, yuansu: ['地'], zhongzu: ['植物'] },
    { guaiwu_id: 1009, guaiwu_mingcheng: '火狐', level: 8, yuansu: ['火'], zhongzu: ['动物'] },
    { guaiwu_id: 1010, guaiwu_mingcheng: '冰晶', level: 7, yuansu: ['水'], zhongzu: ['无机物'] },
    { guaiwu_id: 1011, guaiwu_mingcheng: '雷鸟', level: 10, yuansu: ['风'], zhongzu: ['动物'] },
    { guaiwu_id: 1012, guaiwu_mingcheng: '石巨人', level: 12, yuansu: ['地'], zhongzu: ['无机物'] },
    { guaiwu_id: 1013, guaiwu_mingcheng: '暗影', level: 15, yuansu: ['暗'], zhongzu: ['恶魔'] },
    { guaiwu_id: 1014, guaiwu_mingcheng: '光精灵', level: 14, yuansu: ['圣'], zhongzu: ['精灵'] },
    { guaiwu_id: 1015, guaiwu_mingcheng: '毒蛇', level: 9, yuansu: ['毒'], zhongzu: ['动物'] },
    { guaiwu_id: 1016, guaiwu_mingcheng: '钢铁兽', level: 18, yuansu: ['无'], zhongzu: ['机械'] }
  ];

  // 获取怪物头像表情符号
  const huoquguaiwubiaoqing = (mingcheng) => {
    const biaoqingying = {
      '波利': '🟢',
      '小强': '🪲',
      '蘑菇宝贝': '🍄',
      '红蝙蝠': '🦇',
      '绿水母': '🪼',
      '小鸡': '🐣',
      '蚯蚓': '🪱',
      '树精': '🌳',
      '火狐': '🦊',
      '冰晶': '❄️',
      '雷鸟': '⚡',
      '石巨人': '🗿',
      '暗影': '👤',
      '光精灵': '✨',
      '毒蛇': '🐍',
      '钢铁兽': '🤖'
    };
    return biaoqingying[mingcheng] || '👾';
  };



  // 计算动态卡片宽度
  const jisuan_dongkuaipiankuandu = () => {
    const rongqi = gundongrongqiref.current;
    if (!rongqi) return 160;

    const rongqikuandu = rongqi.clientWidth;
    const shouji = window.matchMedia('(max-width: 768px)').matches;
    const pingban = window.matchMedia('(min-width: 769px) and (max-width: 1024px)').matches;

    let jichu_kapiankuandu, jianju, rongqi_padding;
    if (shouji) {
      jichu_kapiankuandu = 130;
      jianju = 8;
      rongqi_padding = 32;
    } else if (pingban) {
      jichu_kapiankuandu = 150;
      jianju = 10;
      rongqi_padding = 36;
    } else {
      jichu_kapiankuandu = 160;
      jianju = 12;
      rongqi_padding = 40;
    }

    const keyongkuandu = rongqikuandu - rongqi_padding - 15;
    const zuida_xianzhi = shouji ? 3 : (pingban ? 3 : 4);

    let kejianshu = 1;
    for (let n = 1; n <= Math.min(guaiwushuju.length, zuida_xianzhi); n++) {
      const xuyao_kuandu = n * jichu_kapiankuandu + (n - 1) * jianju;
      if (xuyao_kuandu <= keyongkuandu * 0.9) {
        kejianshu = n;
      } else {
        break;
      }
    }

    let dongkuaipiankuandu;
    if (kejianshu === 1) {
      dongkuaipiankuandu = Math.min(keyongkuandu, jichu_kapiankuandu * 1.5);
    } else {
      dongkuaipiankuandu = (keyongkuandu - (kejianshu - 1) * jianju) / kejianshu;
      const zuixiao_kuandu = jichu_kapiankuandu * 0.8;
      const zuida_kuandu = jichu_kapiankuandu * 1.3;
      dongkuaipiankuandu = Math.max(zuixiao_kuandu, Math.min(zuida_kuandu, dongkuaipiankuandu));
    }

    return Math.round(dongkuaipiankuandu);
  };

  // 桌面端鼠标悬浮处理
  const chulishubiaoruyu = () => {
    if (!jiance_zhuomianduan()) return;

    shezhi_shubiaoruyu(true);

    // 清除之前的定时器
    if (xuanfudingshiqiref.current) {
      clearTimeout(xuanfudingshiqiref.current);
    }

    // 设置1.5秒延迟定时器
    xuanfudingshiqiref.current = setTimeout(() => {
      shezhi_yingaixuanfu(true);
      tingzhixunhuangundong();
    }, 1500);
  };

  const chulishubiaolikai = () => {
    if (!jiance_zhuomianduan()) return;

    shezhi_shubiaoruyu(false);
    shezhi_yingaixuanfu(false);

    // 清除定时器
    if (xuanfudingshiqiref.current) {
      clearTimeout(xuanfudingshiqiref.current);
      xuanfudingshiqiref.current = null;
    }

    // 延迟重新开始滚动
    setTimeout(() => {
      if (!shubiaoruyu && !latestchuchuruyu.current) {
        kaishixunhuangundong();
      }
    }, 300);
  };

  // 无限循环滚动函数
  const kaishixunhuangundong = () => {
    const rongqi = gundongrongqiref.current;
    if (!rongqi || latestyingaixuanfu.current || latestchuchuruyu.current || guaiwushuju.length === 0) return;

    const gundongsudu = 0.5;
    const zuida_gundong = rongqi.scrollWidth - rongqi.clientWidth;

    const gundong = () => {
      if (latestyingaixuanfu.current || latestchuchuruyu.current) {
        xunhuangundongref.current = null;
        return;
      }

      rongqi.scrollLeft += gundongsudu;

      if (rongqi.scrollLeft >= zuida_gundong) {
        rongqi.scrollLeft = 0;
      }

      xunhuangundongref.current = requestAnimationFrame(gundong);
    };

    xunhuangundongref.current = requestAnimationFrame(gundong);
  };

  // 停止循环滚动
  const tingzhixunhuangundong = () => {
    if (xunhuangundongref.current) {
      cancelAnimationFrame(xunhuangundongref.current);
      xunhuangundongref.current = null;
    }
  };

  // 处理触摸开始事件
  const chulichuchukaishi = (event) => {
    shezhi_chuchuruyu(true);
    tingzhixunhuangundong();
    // 阻止默认行为，避免页面滚动
    event.preventDefault();
  };

  // 处理触摸结束事件
  const chulichuchujieshu = () => {
    shezhi_chuchuruyu(false);
    // 延迟重新开始滚动
    setTimeout(() => {
      if (!latestyingaixuanfu.current && !latestchuchuruyu.current) {
        kaishixunhuangundong();
      }
    }, 800);
  };



  // 监听容器尺寸变化并计算动态卡片宽度
  useEffect(() => {
    const jisuan_he_gengxin = () => {
      if (gundongrongqiref.current && guaiwushuju.length > 0) {
        const kuandu = jisuan_dongkuaipiankuandu();
        shezhi_dongkuaipiankuandu(kuandu);
      }
    };

    jisuan_he_gengxin();

    const chuli_chuangkouchicun_bianhua = () => {
      setTimeout(jisuan_he_gengxin, 100);
    };

    window.addEventListener('resize', chuli_chuangkouchicun_bianhua);

    return () => {
      window.removeEventListener('resize', chuli_chuangkouchicun_bianhua);
    };
  }, [guaiwushuju.length]);

  // 组件挂载时加载数据
  useEffect(() => {
    const jiazaiguaiwushuju = async () => {
      try {
        shezhi_jiazaizhong(true);
        shezhi_cuowu(null);

        await new Promise(resolve => setTimeout(resolve, 1000));

        shezhi_guaiwushuju(moniGuaiwushuju);

      } catch (error) {
        shezhi_cuowu('加载怪物数据失败，请稍后重试');
      } finally {
        shezhi_jiazaizhong(false);
      }
    };

    jiazaiguaiwushuju();
  }, []);

  // 启动无限循环滚动
  useEffect(() => {
    if (!jiazaizhong && guaiwushuju.length > 0) {
      // 延迟启动滚动，确保DOM已渲染和动画完成
      const timer = setTimeout(() => {
        kaishixunhuangundong();
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [jiazaizhong, guaiwushuju.length]);

  // 页面可见性变化时的处理
  useEffect(() => {
    const chuli_keshixing_bianhua = () => {
      if (document.hidden) {
        tingzhixunhuangundong();
      } else if (!latestyingaixuanfu.current && !latestchuchuruyu.current && guaiwushuju.length > 0) {
        setTimeout(() => {
          kaishixunhuangundong();
        }, 500);
      }
    };

    document.addEventListener('visibilitychange', chuli_keshixing_bianhua);

    return () => {
      document.removeEventListener('visibilitychange', chuli_keshixing_bianhua);
    };
  }, [guaiwushuju.length]);

  // 组件卸载时清理
  useUnmount(() => {
    tingzhixunhuangundong();
    if (xuanfudingshiqiref.current) {
      clearTimeout(xuanfudingshiqiref.current);
    }
  });

  // 处理怪物卡片点击
  const chulidianjikapian = () => {
    // 这里可以添加跳转到怪物详情页的逻辑
  };

  return (
    <Guaiwuliebiaorongqi>
      {/* 标题区域 */}
      <Biaotiquyu>
        <Guaiwutubiao><span>👾</span></Guaiwutubiao>
        <Biaotiwenzi>怪物数据</Biaotiwenzi>
      </Biaotiquyu>

      {/* 内容区域 */}
      {jiazaizhong ? (
        <Jiazaizhuangtai>正在加载怪物数据...</Jiazaizhuangtai>
      ) : cuowu ? (
        <Cuowuzhuangtai>{cuowu}</Cuowuzhuangtai>
      ) : (
        <Guaiwugundongrongqi
          ref={gundongrongqiref}
          onMouseEnter={chulishubiaoruyu}
          onMouseLeave={chulishubiaolikai}
          onTouchStart={chulichuchukaishi}
          onTouchEnd={chulichuchujieshu}
        >
          <Guaiwuliebiaoliebiao>
            <AnimatePresence>
              {guaiwushuju.map((guaiwu, suoyin) => (
                <Guaiwukapian
                  key={guaiwu.guaiwu_id}
                  dongkuaipiankuandu={dongkuaipiankuandu}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3, delay: suoyin * 0.1 }}
                  onClick={() => chulidianjikapian()}
                >
                  <Guaiwutouxiang>
                    <span>{huoquguaiwubiaoqing(guaiwu.guaiwu_mingcheng)}</span>
                  </Guaiwutouxiang>
                  <Guaiwumingcheng>{guaiwu.guaiwu_mingcheng}</Guaiwumingcheng>
                  <Guaiwudengji>Lv.{guaiwu.level}</Guaiwudengji>
                </Guaiwukapian>
              ))}
            </AnimatePresence>
          </Guaiwuliebiaoliebiao>
        </Guaiwugundongrongqi>
      )}
    </Guaiwuliebiaorongqi>
  );
}

export default Guaiwuliebiao;
